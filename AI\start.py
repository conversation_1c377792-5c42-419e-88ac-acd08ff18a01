#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进博会AI助手启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask', 'openai', 'python-dotenv', 'requests', 
        'beautifulsoup4', 'markdown', 'flask-cors'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 依赖包检查通过")
    return True

def check_env_file():
    """检查环境配置文件"""
    env_file = Path('.env')
    if not env_file.exists():
        print("⚠️  警告: 未找到.env文件")
        print("请复制env_example.txt为.env并配置OpenAI API密钥")
        print("命令: cp env_example.txt .env")
        return False
    
    # 检查API密钥是否配置
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
        if 'your_openai_api_key_here' in content:
            print("⚠️  警告: 请在.env文件中配置真实的OpenAI API密钥")
            return False
    
    print("✅ 环境配置检查通过")
    return True

def install_dependencies():
    """安装依赖包"""
    print("📦 正在安装依赖包...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True, text=True)
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 进博会AI助手启动检查")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查依赖包
    if not check_dependencies():
        print("\n📦 是否自动安装依赖包? (y/n): ", end="")
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是']:
            if not install_dependencies():
                sys.exit(1)
        else:
            print("请手动安装依赖包后重试")
            sys.exit(1)
    
    # 检查环境配置
    if not check_env_file():
        print("\n⚠️  环境配置不完整，应用可能无法正常工作")
        print("是否继续启动? (y/n): ", end="")
        choice = input().lower().strip()
        if choice not in ['y', 'yes', '是']:
            sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 所有检查完成！正在启动应用...")
    print("🌐 应用启动后请访问: http://localhost:5000")
    print("按 Ctrl+C 停止应用")
    print("=" * 50)
    
    # 启动Flask应用
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
