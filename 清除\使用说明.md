# 论文文件清理工具使用说明

## 概述
这个工具包包含两个文件，用于安全地删除您电脑上的论文相关文件，同时保留系统文件。

## 文件说明

### 1. delete_papers.ps1 (PowerShell脚本 - 推荐)
- **功能**：智能搜索并删除用户目录中的论文相关文件
- **特点**：
  - 自动排除系统目录
  - 显示删除进度
  - 详细的文件列表预览
  - 错误处理和报告
  - 支持中文路径

### 2. delete_papers.bat (批处理文件)
- **功能**：快速删除指定目录中的论文文件
- **特点**：
  - 操作简单快速
  - 适合不熟悉PowerShell的用户
  - 基本的文件类型支持

## 使用方法

### 方法一：使用PowerShell脚本（推荐）

1. **右键点击** `delete_papers.ps1` 文件
2. 选择 **"使用PowerShell运行"**
3. 按照提示输入确认信息：
   - 第一次确认：输入 `yes`
   - 最终确认：输入 `DELETE`
4. 等待脚本完成

### 方法二：使用批处理文件

1. **双击** `delete_papers.bat` 文件
2. 输入 `yes` 确认删除
3. 等待完成

### 方法三：通过命令行运行

1. 按 `Win + R`，输入 `powershell`
2. 导航到脚本所在目录：`cd "C:\Users\<USER>\Desktop\清除"`
3. 运行脚本：`.\delete_papers.ps1`

## 安全特性

✅ **保护系统文件**：自动排除系统目录和程序文件
✅ **用户确认**：需要多次确认才能执行删除
✅ **文件预览**：删除前显示将要删除的文件列表
✅ **错误处理**：记录删除失败的文件
✅ **进度显示**：实时显示删除进度

## 将要删除的文件类型

- 📄 PDF文件 (.pdf)
- 📝 Word文档 (.doc, .docx)
- 📄 文本文档 (.txt)
- 📄 RTF文档 (.rtf)

## 搜索的目录

- 🖥️ 桌面 (Desktop)
- 📁 文档 (Documents)
- ⬇️ 下载 (Downloads)
- 📚 论文文件夹
- 🏘️ 乡土文件夹
- 💼 工作文件夹 (work)
- 💬 微信文件 (xwechat_files)
- ☁️ OneDrive云存储

## 注意事项

⚠️ **重要提醒**：
1. 删除操作**不可逆**，请确保文件确实不需要
2. 建议先备份重要文件
3. 脚本会保留系统文件和程序文件
4. 某些文件可能因权限问题无法删除

## 故障排除

### 如果脚本无法运行：
1. 检查PowerShell执行策略：`Get-ExecutionPolicy`
2. 临时允许脚本执行：`Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

### 如果某些文件无法删除：
1. 检查文件是否被其他程序占用
2. 检查文件权限
3. 手动删除剩余文件

## 联系支持

如果遇到问题，请检查：
1. 文件路径是否正确
2. 是否有足够的权限
3. 文件是否被其他程序占用

---

**使用前请仔细阅读说明，确保理解操作后果！**
