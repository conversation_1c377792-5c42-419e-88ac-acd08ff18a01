# 📁 进博会AI助手 - 项目结构

```
进博会AI助手/
├── 📄 app.py                    # 主应用文件 (Flask后端)
├── 📄 requirements.txt          # Python依赖包列表
├── 📄 start.py                  # 智能启动脚本
├── 📄 start.bat                 # Windows启动批处理文件
├── 📄 test_app.py               # 功能测试脚本
├── 📄 env_example.txt           # 环境变量配置示例
├── 📄 README.md                 # 项目说明文档
├── 📄 示例问题.md               # 可问问题示例
├── 📄 项目结构.md               # 本文件
└── 📁 templates/                # HTML模板目录
    └── 📄 index.html            # 主页面模板
```

## 🔧 核心文件说明

### 后端文件
- **`app.py`**: Flask应用主文件，包含AI聊天逻辑、知识库和API接口
- **`requirements.txt`**: 项目依赖包列表，用于pip安装

### 启动文件
- **`start.py`**: 智能启动脚本，自动检查环境、依赖和配置
- **`start.bat`**: Windows用户双击启动的批处理文件

### 配置文件
- **`env_example.txt`**: 环境变量配置示例，需要复制为`.env`并填入API密钥

### 前端文件
- **`templates/index.html`**: 现代化的Web界面，包含聊天功能和知识展示

### 文档文件
- **`README.md`**: 详细的项目说明和使用指南
- **`示例问题.md`**: 展示AI助手能力的示例问题
- **`项目结构.md`**: 本文件，项目结构说明

### 测试文件
- **`test_app.py`**: 功能测试脚本，验证应用是否正常工作

## 🚀 快速启动流程

1. **环境准备**: 确保安装了Python 3.7+
2. **安装依赖**: `pip install -r requirements.txt`
3. **配置API**: 复制`env_example.txt`为`.env`并填入OpenAI API密钥
4. **启动应用**: 运行`start.py`或双击`start.bat`
5. **访问应用**: 浏览器打开 http://localhost:5000

## 📱 功能模块

### 智能问答模块
- 基于OpenAI GPT-3.5-turbo的AI问答
- 本地知识库备用回答
- 实时聊天界面

### 知识库模块
- 进博会基本信息
- 志愿者服务详情
- 展区设置说明
- 历届亮点数据

### Web界面模块
- 响应式设计，支持移动端
- 现代化UI设计
- 实时知识库展示

## 🔄 数据流

```
用户输入 → Web界面 → Flask后端 → OpenAI API → AI回答 → 用户界面
                ↓
            本地知识库 (备用)
```

## 🛠️ 技术栈

- **后端**: Python + Flask
- **前端**: HTML5 + CSS3 + JavaScript
- **AI服务**: OpenAI GPT-3.5-turbo
- **数据存储**: 本地结构化数据
- **部署**: 本地运行，支持Docker化

## 📊 项目特点

- ✅ **开箱即用**: 完整的项目结构，一键启动
- ✅ **智能问答**: 基于先进AI技术
- ✅ **本地备用**: API不可用时仍可正常使用
- ✅ **现代化界面**: 美观的Web界面
- ✅ **移动适配**: 支持各种设备
- ✅ **易于扩展**: 模块化设计，便于添加新功能

## 🔑 环境变量配置

在使用本项目之前，请先配置环境变量文件`.env`，并填写您的OpenAI API密钥。

```
ARK_API_KEY=你的API密钥
```
