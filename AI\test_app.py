#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进博会AI助手测试脚本
"""

import requests
import json
import time

def test_local_response():
    """测试本地知识库回答功能"""
    print("🧪 测试本地知识库回答功能...")
    
    from app import get_local_response
    
    test_questions = [
        "什么是进博会志愿者？",
        "进博会什么时候举办？",
        "进博会有哪些展区？",
        "如何成为志愿者？"
    ]
    
    for question in test_questions:
        print(f"\n问题: {question}")
        response = get_local_response(question)
        print(f"回答: {response[:100]}...")
        time.sleep(0.5)
    
    print("\n✅ 本地知识库测试完成")

def test_knowledge_api():
    """测试知识库API"""
    print("\n🧪 测试知识库API...")
    
    try:
        response = requests.get('http://localhost:5000/api/knowledge')
        if response.status_code == 200:
            knowledge = response.json()
            print(f"✅ 知识库API正常，包含 {len(knowledge)} 个分类")
            for category in knowledge.keys():
                print(f"  - {category}")
        else:
            print(f"❌ 知识库API返回错误状态码: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到应用，请确保应用正在运行")
    except Exception as e:
        print(f"❌ 测试知识库API时出错: {e}")

def test_chat_api():
    """测试聊天API"""
    print("\n🧪 测试聊天API...")
    
    test_message = "什么是进博会？"
    
    try:
        response = requests.post('http://localhost:5000/api/chat', 
                               json={'message': test_message},
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 聊天API正常")
            print(f"问题: {test_message}")
            print(f"回答: {data['response'][:100]}...")
        else:
            print(f"❌ 聊天API返回错误状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到应用，请确保应用正在运行")
    except Exception as e:
        print(f"❌ 测试聊天API时出错: {e}")

def main():
    """主测试函数"""
    print("🚀 进博会AI助手功能测试")
    print("=" * 50)
    
    # 测试本地功能
    test_local_response()
    
    print("\n" + "=" * 50)
    print("📝 测试说明:")
    print("1. 本地知识库测试: 测试应用的核心回答功能")
    print("2. 知识库API测试: 测试知识库数据接口")
    print("3. 聊天API测试: 测试AI问答接口")
    print("\n注意: API测试需要应用正在运行")
    print("请先运行 'python app.py' 启动应用")
    
    # 询问是否测试API
    print("\n是否测试API功能? (需要应用正在运行) (y/n): ", end="")
    choice = input().lower().strip()
    
    if choice in ['y', 'yes', '是']:
        test_knowledge_api()
        test_chat_api()
    
    print("\n🎉 测试完成！")

if __name__ == '__main__':
    main()
