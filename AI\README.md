# 🇨🇳 进博会AI助手

一个专门了解进博会全部知识的智能AI助手，能够回答所有关于进博会的问题，特别针对志愿者需求定制。

## ✨ 功能特色

- 🤖 **智能问答**: 基于先进AI技术，准确回答进博会相关问题
- 📚 **全面知识**: 涵盖进博会所有方面的详细信息
- 🎯 **专业服务**: 特别针对志愿者需求定制
- 💡 **实时更新**: 知识库持续更新，信息最新最准确
- 🌐 **现代化界面**: 美观的Web界面，支持移动端

## 🚀 快速开始

### 环境要求

- Python 3.7+
- 网络连接（用于OpenAI API调用）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd 进博会AI助手
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置API密钥**
   - 复制 `env_example.txt` 为 `.env`
   - 在 `.env` 文件中填入您的OpenAI API密钥
   ```bash
   cp env_example.txt .env
   # 编辑 .env 文件，填入您的API密钥
   ```

4. **运行应用**
   ```bash
   python app.py
   ```

5. **访问应用**
   打开浏览器访问: http://localhost:5000

## 📖 知识库内容

### 基本信息
- 展会名称、性质、主办方
- 举办地点、时间、频率
- 展会主题和特色

### 志愿者服务
- 招募对象和要求
- 服务内容和职责
- 培训内容和时间
- 志愿者精神

### 展会特色
- 六大展区详细介绍
- 新产品、新技术展示
- 服务贸易相关内容

### 历届亮点
- 2018-2023年各届数据
- 参展国家和组织数量
- 重要成果和影响

## 🔧 技术架构

- **后端**: Flask (Python)
- **前端**: HTML5 + CSS3 + JavaScript
- **AI服务**: OpenAI GPT-3.5-turbo
- **知识库**: 本地结构化数据 + AI增强

## 📱 使用说明

1. **智能问答**: 在左侧聊天框中输入您的问题
2. **知识浏览**: 右侧实时显示完整的知识库内容
3. **移动适配**: 支持手机、平板等移动设备

## 🤝 常见问题

### Q: 如何获取OpenAI API密钥？
A: 访问 https://platform.openai.com/api-keys 注册账号并创建API密钥

### Q: 如果API调用失败怎么办？
A: 系统会自动切换到本地知识库模式，确保基本功能可用

### Q: 支持哪些语言？
A: 目前主要支持中文，AI回答也使用中文

## 🔄 更新日志

- **v1.0.0**: 初始版本，包含基础问答功能和知识库
- 支持OpenAI API集成
- 现代化Web界面
- 完整的进博会知识库

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 请确保遵守OpenAI的使用条款和API限制。本项目仅用于教育和研究目的。
