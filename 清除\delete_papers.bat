@echo off
chcp 65001 >nul
title 论文文件清理工具

echo ========================================
echo           论文文件清理工具
echo ========================================
echo.
echo 此工具将删除以下类型的文件：
echo - PDF文件 (.pdf)
echo - Word文档 (.doc, .docx)  
echo - 文本文档 (.txt)
echo - RTF文档 (.rtf)
echo.
echo 注意：只删除用户文件，系统文件将被保留
echo.

set /p confirm="您确定要删除所有论文相关文件吗？(输入 'yes' 确认): "
if /i not "%confirm%"=="yes" (
    echo 操作已取消
    pause
    exit /b
)

echo.
echo 开始搜索并删除文件...
echo.

set deleted_count=0
set total_size=0

REM 删除桌面文件
if exist "%USERPROFILE%\Desktop\*.pdf" (
    echo 删除桌面PDF文件...
    del /q "%USERPROFILE%\Desktop\*.pdf" 2>nul
    for %%f in ("%USERPROFILE%\Desktop\*.pdf") do set /a deleted_count+=1
)

if exist "%USERPROFILE%\Desktop\*.doc" (
    echo 删除桌面DOC文件...
    del /q "%USERPROFILE%\Desktop\*.doc" 2>nul
    for %%f in ("%USERPROFILE%\Desktop\*.doc") do set /a deleted_count+=1
)

if exist "%USERPROFILE%\Desktop\*.docx" (
    echo 删除桌面DOCX文件...
    del /q "%USERPROFILE%\Desktop\*.docx" 2>nul
    for %%f in ("%USERPROFILE%\Desktop\*.docx") do set /a deleted_count+=1
)

if exist "%USERPROFILE%\Desktop\*.txt" (
    echo 删除桌面TXT文件...
    del /q "%USERPROFILE%\Desktop\*.txt" 2>nul
    for %%f in ("%USERPROFILE%\Desktop\*.txt") do set /a deleted_count+=1
)

REM 删除下载文件夹文件
if exist "%USERPROFILE%\Downloads\*.pdf" (
    echo 删除下载文件夹PDF文件...
    del /q "%USERPROFILE%\Downloads\*.pdf" 2>nul
    for %%f in ("%USERPROFILE%\Downloads\*.pdf") do set /a deleted_count+=1
)

if exist "%USERPROFILE%\Downloads\*.doc" (
    echo 删除下载文件夹DOC文件...
    del /q "%USERPROFILE%\Downloads\*.doc" 2>nul
    for %%f in ("%USERPROFILE%\Downloads\*.doc") do set /a deleted_count+=1
)

if exist "%USERPROFILE%\Downloads\*.docx" (
    echo 删除下载文件夹DOCX文件...
    del /q "%USERPROFILE%\Downloads\*.docx" 2>nul
    for %%f in ("%USERPROFILE%\Downloads\*.docx") do set /a deleted_count+=1
)

if exist "%USERPROFILE%\Downloads\*.txt" (
    echo 删除下载文件夹TXT文件...
    del /q "%USERPROFILE%\Downloads\*.txt" 2>nul
    for %%f in ("%USERPROFILE%\Downloads\*.txt") do set /a deleted_count+=1
)

REM 删除论文文件夹
if exist "%USERPROFILE%\论文" (
    echo 删除论文文件夹...
    rmdir /s /q "%USERPROFILE%\论文" 2>nul
    set /a deleted_count+=1
)

REM 删除乡土文件夹
if exist "%USERPROFILE%\乡土" (
    echo 删除乡土文件夹...
    rmdir /s /q "%USERPROFILE%\乡土" 2>nul
    set /a deleted_count+=1
)

REM 删除work文件夹中的文档
if exist "%USERPROFILE%\work" (
    echo 删除work文件夹中的文档...
    del /s /q "%USERPROFILE%\work\*.pdf" 2>nul
    del /s /q "%USERPROFILE%\work\*.doc" 2>nul
    del /s /q "%USERPROFILE%\work\*.docx" 2>nul
    del /s /q "%USERPROFILE%\work\*.txt" 2>nul
)

echo.
echo ========================================
echo           清理完成！
echo ========================================
echo 已删除 %deleted_count% 个文件/文件夹
echo.
echo 注意：某些文件可能因权限问题无法删除
echo 建议手动检查剩余文件
echo.
pause
