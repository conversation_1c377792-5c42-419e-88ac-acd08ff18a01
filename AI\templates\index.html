<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进博会AI助手 - 您的专属进博会知识顾问</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .chat-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            height: 600px;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chat-header h2 {
            color: #667eea;
            font-size: 1.8rem;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 15px;
            max-height: 400px;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: #667eea;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: white;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .chat-input {
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input input:focus {
            border-color: #667eea;
        }

        .chat-input button {
            padding: 15px 25px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .chat-input button:hover {
            background: #5a6fd8;
        }

        .knowledge-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            height: 600px;
            overflow-y: auto;
        }

        .knowledge-header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .knowledge-header h2 {
            color: #667eea;
            font-size: 1.8rem;
        }

        .knowledge-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        .knowledge-item h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .knowledge-item ul {
            list-style: none;
            padding-left: 0;
        }

        .knowledge-item li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .knowledge-item li:last-child {
            border-bottom: none;
        }

        .features {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }

        .features h2 {
            color: #667eea;
            margin-bottom: 25px;
            font-size: 2rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }

        .feature-card {
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .feature-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }

        .loading {
            text-align: center;
            color: #667eea;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇨🇳 进博会AI助手</h1>
            <p>您的专属进博会知识顾问，了解一切关于进博会的信息</p>
        </div>

        <div class="main-content">
            <div class="chat-section">
                <div class="chat-header">
                    <h2>💬 智能问答</h2>
                </div>
                <div class="chat-messages" id="chatMessages">
                    <div class="message ai-message">
                        您好！我是进博会AI助手，很高兴为您服务！<br><br>
                        我可以回答关于进博会的各种问题，包括：<br>
                        • 展会基本信息<br>
                        • 志愿者服务<br>
                        • 展区设置<br>
                        • 历届亮点<br>
                        • 参展信息等<br><br>
                        请告诉我您想了解什么，我会尽力帮助您！
                    </div>
                </div>
                <div class="chat-input">
                    <input type="text" id="userInput" placeholder="请输入您的问题..." onkeypress="handleKeyPress(event)">
                    <button onclick="sendMessage()">发送</button>
                </div>
            </div>

            <div class="knowledge-section">
                <div class="knowledge-header">
                    <h2>📚 知识库</h2>
                </div>
                <div id="knowledgeContent">
                    <!-- 知识库内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>

        <div class="features">
            <h2>✨ 功能特色</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>🤖 智能问答</h3>
                    <p>基于先进AI技术，准确回答进博会相关问题</p>
                </div>
                <div class="feature-card">
                    <h3>📖 全面知识</h3>
                    <p>涵盖进博会所有方面的详细信息</p>
                </div>
                <div class="feature-card">
                    <h3>🎯 专业服务</h3>
                    <p>特别针对志愿者需求定制</p>
                </div>
                <div class="feature-card">
                    <h3>💡 实时更新</h3>
                    <p>知识库持续更新，信息最新最准确</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 加载知识库内容
        async function loadKnowledge() {
            try {
                const response = await fetch('/api/knowledge');
                const knowledge = await response.json();
                displayKnowledge(knowledge);
            } catch (error) {
                console.error('加载知识库失败:', error);
            }
        }

        // 显示知识库内容
        function displayKnowledge(knowledge) {
            const container = document.getElementById('knowledgeContent');
            let html = '';

            for (const [category, content] of Object.entries(knowledge)) {
                html += `
                    <div class="knowledge-item">
                        <h3>${category}</h3>
                        <ul>
                `;
                
                for (const [key, value] of Object.entries(content)) {
                    html += `<li><strong>${key}:</strong> ${value}</li>`;
                }
                
                html += `
                        </ul>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('userInput');
            const message = input.value.trim();
            
            if (!message) return;

            // 添加用户消息
            addMessage(message, 'user');
            input.value = '';

            // 显示加载状态
            const loadingId = addMessage('正在思考中...', 'ai', 'loading');

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.error) {
                    updateMessage(loadingId, `抱歉，出现错误：${data.error}`, 'ai');
                } else {
                    updateMessage(loadingId, data.response, 'ai');
                }
            } catch (error) {
                updateMessage(loadingId, '抱歉，网络连接出现问题，请稍后重试。', 'ai');
            }
        }

        // 添加消息到聊天界面
        function addMessage(content, type, className = '') {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const messageId = Date.now();
            
            messageDiv.id = `message-${messageId}`;
            messageDiv.className = `message ${type}-message ${className}`;
            messageDiv.innerHTML = content;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            return messageId;
        }

        // 更新消息内容
        function updateMessage(messageId, content, type) {
            const messageDiv = document.getElementById(`message-${messageId}`);
            if (messageDiv) {
                messageDiv.className = `message ${type}-message`;
                messageDiv.innerHTML = content;
            }
        }

        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadKnowledge();
        });
    </script>
</body>
</html>
