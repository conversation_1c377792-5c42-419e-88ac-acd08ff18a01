
from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import requests
import os
from dotenv import load_dotenv
import json
import markdown

# 加载环境变量
load_dotenv()

app = Flask(__name__)
CORS(app)



# 配置豆包API
ARK_API_KEY = os.getenv('ARK_API_KEY')

# 进博会知识库
CIIE_KNOWLEDGE = {
    "基本信息": {
        "名称": "中国国际进口博览会（China International Import Expo，简称CIIE）",
        "简称": "进博会",
        "性质": "世界上第一个以进口为主题的国家级展会",
        "主办方": "中华人民共和国商务部、上海市人民政府",
        "举办地": "上海",
        "首届": "2018年（首届中国国际进口博览会于2018年在上海国家会展中心举行）",
        "频率": "每年一届"
    },
    "志愿者服务": {
        "招募对象": "在校大学生、社会人士等",
        "服务内容": "展会引导、信息咨询、语言翻译、秩序维护等",
        "培训内容": "进博会知识、服务礼仪、应急处理、外语培训等",
        "服务时间": "展会期间（通常为11月）",
        "志愿者精神": "奉献、友爱、互助、进步"
    },
    "展会特色": {
        "主题": "新时代，共享未来",
        "六大展区": "食品及农产品、汽车、技术装备、消费品、医疗器械及医药保健、服务贸易",
        "亮点": "新产品首发、新技术展示、新服务体验",
        "规模": "全球规模最大的进口博览会"
    },
    "历届亮点": {
        "2018年": "首届进博会，172个国家、地区和国际组织参会",
        "2019年": "181个国家、地区和国际组织参会",
        "2020年": "affected by the pandemic",
        "2021年": "127个国家和地区参展",
        "2022年": "145个国家、地区和国际组织参展",
        "2023年": "154个国家、地区和国际组织参展"
    }
}

def get_cii_ai_response(user_question):
    # 直接用 requests 调用豆包API，AI联网自由回答
    try:
        url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {ARK_API_KEY}"
        }
        system_prompt = "你是一个智能AI助手，请联网并用中文准确、详细、友好地回答用户的任何问题。"
        data = {
            "model": "doubao-seed-1-6-250615",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_question}
            ]
        }
        response = requests.post(url, headers=headers, json=data, timeout=60)
        res_json = response.json()
        # 兼容不同返回格式
        if "choices" in res_json and res_json["choices"]:
            msg = res_json["choices"][0].get("message")
            if isinstance(msg, dict):
                return msg.get("content", str(msg))
            return str(msg)
        return str(res_json)
    except Exception as e:
        return f"豆包AI助手出错：{str(e)}"
def get_local_response(user_question):
    """本地知识库回答（当API不可用时）"""
    
    # 简单的关键词匹配
    question_lower = user_question.lower()
    
    if "志愿者" in question_lower or "志愿" in question_lower:
        return """关于进博会志愿者服务：
    
        🎯 **招募对象**：在校大学生、社会人士等
        📋 **服务内容**：展会引导、信息咨询、语言翻译、秩序维护等
        📚 **培训内容**：进博会知识、服务礼仪、应急处理、外语培训等
        ⏰ **服务时间**：展会期间（通常为11月）
        💝 **志愿者精神**：奉献、友爱、互助、进步
    
        如果您想成为进博会志愿者，建议关注官方发布的招募信息！"""
    
    elif "时间" in question_lower or "举办" in question_lower:
        return """进博会举办信息：
    
        📅 **举办时间**：每年一届，通常在11月举办
        📍 **举办地点**：上海
        🏛️ **主办方**：中华人民共和国商务部、上海市人民政府
        🎪 **性质**：世界上第一个以进口为主题的国家级展会"""
    
    elif "展区" in question_lower or "展品" in question_lower:
        return """进博会六大展区：
    
        🥗 **食品及农产品**：各国特色美食、农产品
        🚗 **汽车**：新能源汽车、智能驾驶技术
        ⚙️ **技术装备**：智能制造、工业设备
        🛍️ **消费品**：时尚用品、家居用品
        🏥 **医疗器械及医药保健**：医疗技术、健康产品
        💼 **服务贸易**：金融服务、物流服务等"""
    
    else:
        return """您好！我是进博会AI助手，很高兴为您服务！
    
        我可以回答关于进博会的各种问题，包括：
        - 展会基本信息
        - 志愿者服务
        - 展区设置
        - 历届亮点
        - 参展信息等
    
        请告诉我您想了解什么，我会尽力帮助您！"""

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """聊天API接口"""
    try:
        data = request.get_json()
        user_message = data.get('message', '')
        
        if not user_message:
            return jsonify({'error': '请输入您的问题'}), 400
        
        # 获取AI回答
        ai_response = get_cii_ai_response(user_message)
        
        return jsonify({
            'response': ai_response,
            'timestamp': '2024-01-01 12:00:00'
        })
        
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500

@app.route('/api/knowledge')
def get_knowledge():
    """获取知识库信息"""
    return jsonify(CIIE_KNOWLEDGE)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
