# 论文文件删除脚本 - 安全版本
# 此脚本只删除用户文件，保留系统文件

Write-Host "=== 论文文件清理脚本 ===" -ForegroundColor Green
Write-Host "此脚本将删除用户目录中的论文相关文件" -ForegroundColor Yellow
Write-Host "系统文件将被保留" -ForegroundColor Yellow
Write-Host ""

# 确认操作
$confirm = Read-Host "您确定要删除所有论文相关文件吗？(输入 'yes' 确认)"
if ($confirm -ne "yes") {
    Write-Host "操作已取消" -ForegroundColor Red
    exit
}

# 定义要删除的文件扩展名
$extensions = @("*.pdf", "*.doc", "*.docx", "*.txt", "*.rtf")

# 定义要搜索的用户目录（排除系统目录）
$userDirectories = @(
    "$env:USERPROFILE\Desktop",
    "$env:USERPROFILE\Documents", 
    "$env:USERPROFILE\Downloads",
    "$env:USERPROFILE\论文",
    "$env:USERPROFILE\乡土",
    "$env:USERPROFILE\work",
    "$env:USERPROFILE\xwechat_files",
    "$env:USERPROFILE\OneDrive"
)

# 定义要排除的系统目录
$excludeDirectories = @(
    "$env:USERPROFILE\.cursor",
    "$env:USERPROFILE\.vscode", 
    "$env:USERPROFILE\.trae",
    "$env:USERPROFILE\.trae-cn",
    "$env:USERPROFILE\AppData",
    "$env:USERPROFILE\Local Settings",
    "$env:USERPROFILE\NTUSER.DAT*",
    "$env:USERPROFILE\ntuser.ini"
)

Write-Host "开始搜索论文相关文件..." -ForegroundColor Cyan

$filesToDelete = @()
$totalSize = 0

foreach ($dir in $userDirectories) {
    if (Test-Path $dir) {
        Write-Host "搜索目录: $dir" -ForegroundColor Gray
        
        foreach ($ext in $extensions) {
            try {
                $files = Get-ChildItem -Path $dir -Recurse -Filter $ext -ErrorAction SilentlyContinue | 
                         Where-Object { $_.FullName -notmatch "\\\.(cursor|vscode|trae|AppData)\\" }
                
                foreach ($file in $files) {
                    # 检查是否在排除目录中
                    $shouldExclude = $false
                    foreach ($excludeDir in $excludeDirectories) {
                        if ($file.FullName -like "$excludeDir*") {
                            $shouldExclude = $true
                            break
                        }
                    }
                    
                    if (-not $shouldExclude) {
                        $filesToDelete += $file
                        $totalSize += $file.Length
                    }
                }
            }
            catch {
                Write-Host "搜索 $dir 时出错: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

# 显示将要删除的文件
Write-Host ""
Write-Host "找到 $($filesToDelete.Count) 个文件，总大小: $([math]::Round($totalSize / 1MB, 2)) MB" -ForegroundColor Yellow

if ($filesToDelete.Count -eq 0) {
    Write-Host "没有找到需要删除的文件" -ForegroundColor Green
    exit
}

# 显示前20个文件作为预览
Write-Host ""
Write-Host "前20个文件预览:" -ForegroundColor Cyan
$filesToDelete | Select-Object -First 20 | ForEach-Object {
    Write-Host "  $($_.Name) - $($_.FullName)" -ForegroundColor Gray
}

if ($filesToDelete.Count > 20) {
    Write-Host "  ... 还有 $($filesToDelete.Count - 20) 个文件" -ForegroundColor Gray
}

Write-Host ""
$finalConfirm = Read-Host "确认删除这些文件吗？(输入 'DELETE' 确认)"
if ($finalConfirm -ne "DELETE") {
    Write-Host "操作已取消" -ForegroundColor Red
    exit
}

# 开始删除文件
Write-Host ""
Write-Host "开始删除文件..." -ForegroundColor Green

$deletedCount = 0
$deletedSize = 0
$errors = @()

foreach ($file in $filesToDelete) {
    try {
        $fileSize = $file.Length
        Remove-Item $file.FullName -Force
        $deletedCount++
        $deletedSize += $fileSize
        Write-Host "已删除: $($file.Name)" -ForegroundColor Green
        
        # 显示进度
        $progress = [math]::Round(($deletedCount / $filesToDelete.Count) * 100, 1)
        Write-Progress -Activity "删除文件" -Status "进度: $progress%" -PercentComplete $progress
    }
    catch {
        $errors += "删除失败: $($file.FullName) - $($_.Exception.Message)"
        Write-Host "删除失败: $($file.Name)" -ForegroundColor Red
    }
}

Write-Progress -Activity "删除文件" -Completed

# 显示删除结果
Write-Host ""
Write-Host "=== 删除完成 ===" -ForegroundColor Green
Write-Host "成功删除: $deletedCount 个文件" -ForegroundColor Green
Write-Host "释放空间: $([math]::Round($deletedSize / 1MB, 2)) MB" -ForegroundColor Green

if ($errors.Count -gt 0) {
    Write-Host ""
    Write-Host "删除失败的文件:" -ForegroundColor Red
    foreach ($error in $errors) {
        Write-Host "  $error" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "清理完成！" -ForegroundColor Green
Read-Host "按回车键退出"
